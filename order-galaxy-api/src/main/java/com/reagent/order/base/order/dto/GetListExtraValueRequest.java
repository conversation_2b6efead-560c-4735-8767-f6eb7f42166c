package com.reagent.order.base.order.dto;

import java.io.Serializable;
import java.util.List;

/**
 * 查询List类型extraValue请求DTO
 */
public class GetListExtraValueRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单ID
     */
    private Integer orderId;

    /**
     * 扩展键列表（支持同时查询多个extraKey）
     */
    private List<Integer> extraKeys;

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public List<Integer> getExtraKeys() {
        return extraKeys;
    }

    public void setExtraKeys(List<Integer> extraKeys) {
        this.extraKeys = extraKeys;
    }

    @Override
    public String toString() {
        return "GetListExtraValueRequest{" +
                "orderId=" + orderId +
                ", extraKeys=" + extraKeys +
                '}';
    }
}
