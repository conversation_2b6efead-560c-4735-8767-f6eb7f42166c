package com.reagent.order.base.order.service;

import com.reagent.order.base.order.dto.BaseOrderExtraDTO;
import com.ruijing.fundamental.api.remote.RemoteResponse;

import java.util.Collection;
import java.util.List;

/**
 * @Author: <PERSON>g <PERSON>
 * @Description:
 * @DateTime: 2021/6/28 15:28
 */
public interface OrderExtraRpcService {

    /**
     * 批量插入列表
     * @param baseOrderExtraDTOList
     * @return
     */
    RemoteResponse<Integer> insertList(List<BaseOrderExtraDTO> baseOrderExtraDTOList);

    /**
     * 批量更新列表
     * @param baseOrderExtraDTOList 数据
     * @return 是否成功
     */
    RemoteResponse<Boolean> saveList(List<BaseOrderExtraDTO> baseOrderExtraDTOList);

    /**
     * 通过订单id集合选择条目
     * @param orderIdCollection
     * @return
     */
    RemoteResponse<List<BaseOrderExtraDTO>> selectByOrderIdList(Collection<Integer> orderIdCollection);

    /**
     * 通过订单号集合选择条目
     * @param orderNoCollection
     * @return
     */
    RemoteResponse<List<BaseOrderExtraDTO>> selectByOrderNoList(Collection<String> orderNoCollection);

    /**
     * 通过订单id和操作类型id（extra value）查询条目
     * @param query
     * @return
     */
    RemoteResponse<List<BaseOrderExtraDTO>> selectByOrderIdAndExtraKey(BaseOrderExtraDTO query);

    /**
     * 通过订单id列表和操作类型id（extra value）查询条目
     * @param orderIdList
     * @param extraKey
     * @return
     */
    RemoteResponse<List<BaseOrderExtraDTO>> selectByOrderIdInAndExtraKey(Collection<Integer> orderIdList, Integer extraKey);

    /**
     * 根据订单id删除订单额外信息
     * @param orderIdCol
     * @return
     */
    RemoteResponse<Boolean> deleteInOrderId(Collection<Integer> orderIdCol);

    /**
     * 追加元素到List类型的extraValue中
     * 支持ACCEPT_APPROVE_USERS(12)和ORDER_TAG(42)等List类型的extraKey
     *
     * @param orderId 订单ID
     * @param extraKey 扩展键（12或42）
     * @param appendValues 要追加的值列表
     * @return 操作结果
     */
    RemoteResponse<Boolean> appendToListExtraValue(Integer orderId, Integer extraKey, List<Object> appendValues);

    /**
     * 查询List类型的extraValue并解析为List
     * 支持ACCEPT_APPROVE_USERS(12)和ORDER_TAG(42)等List类型的extraKey
     *
     * @param orderId 订单ID
     * @param extraKey 扩展键（12或42）
     * @return 解析后的List数据
     */
    RemoteResponse<List<Object>> getListExtraValue(Integer orderId, Integer extraKey);
}
