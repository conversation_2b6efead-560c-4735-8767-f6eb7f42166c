package com.reagent.order.base.order.dto;

import java.io.Serializable;
import java.util.List;

/**
 * 追加List类型extraValue请求DTO
 */
public class AppendListExtraValueRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单ID
     */
    private Integer orderId;

    /**
     * 扩展键（12或42）
     */
    private Integer extraKey;

    /**
     * 要追加的值列表
     */
    private List<Object> appendValues;

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public Integer getExtraKey() {
        return extraKey;
    }

    public void setExtraKey(Integer extraKey) {
        this.extraKey = extraKey;
    }

    public List<Object> getAppendValues() {
        return appendValues;
    }

    public void setAppendValues(List<Object> appendValues) {
        this.appendValues = appendValues;
    }

    @Override
    public String toString() {
        return "AppendListExtraValueRequest{" +
                "orderId=" + orderId +
                ", extraKey=" + extraKey +
                ", appendValues=" + appendValues +
                '}';
    }
}
