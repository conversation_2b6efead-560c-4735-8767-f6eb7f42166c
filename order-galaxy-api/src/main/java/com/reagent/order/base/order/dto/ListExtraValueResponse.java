package com.reagent.order.base.order.dto;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * List类型extraValue响应DTO
 */
public class ListExtraValueResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单ID
     */
    private Integer orderId;

    /**
     * extraKey对应的List数据
     * key: extraKey, value: List数据
     */
    private Map<Integer, List<Object>> extraValueMap;

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public Map<Integer, List<Object>> getExtraValueMap() {
        return extraValueMap;
    }

    public void setExtraValueMap(Map<Integer, List<Object>> extraValueMap) {
        this.extraValueMap = extraValueMap;
    }

    @Override
    public String toString() {
        return "ListExtraValueResponse{" +
                "orderId=" + orderId +
                ", extraValueMap=" + extraValueMap +
                '}';
    }
}
