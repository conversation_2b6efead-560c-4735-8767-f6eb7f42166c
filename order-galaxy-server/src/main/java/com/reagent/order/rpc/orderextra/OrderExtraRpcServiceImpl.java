package com.reagent.order.rpc.orderextra;

import com.reagent.order.base.order.dto.AppendListExtraValueRequest;
import com.reagent.order.base.order.dto.BaseOrderExtraDTO;
import com.reagent.order.base.order.dto.GetListExtraValueRequest;
import com.reagent.order.base.order.dto.ListExtraValueResponse;
import com.reagent.order.base.order.mapper.OrderExtraMapper;
import com.reagent.order.base.order.model.OrderExtraDO;
import com.reagent.order.base.order.service.OrderExtraRpcService;
import com.reagent.order.base.order.translator.OrderExtraTranslator;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.store.order.api.base.orderextra.enums.OrderExtraEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: Zeng Yanru
 * @Description:
 * @DateTime: 2021/6/28 16:00
 */
@MSharpService
public class OrderExtraRpcServiceImpl implements OrderExtraRpcService {

    @Resource
    private OrderExtraMapper orderExtraMapper;

    /**
     * 批量插入列表
     *
     * @param baseOrderExtraDTOList
     * @return
     */
    @Override
    public RemoteResponse<Integer> insertList(List<BaseOrderExtraDTO> baseOrderExtraDTOList) {
        Preconditions.notEmpty(baseOrderExtraDTOList, "批量插入订单扩展表入参不可为空");
        List<OrderExtraDO> orderExtraDOList = OrderExtraTranslator.dtoListToDoList(baseOrderExtraDTOList);
        int successCount = orderExtraMapper.insertList(orderExtraDOList);
        Preconditions.isTrue(successCount > 0, "插入订单扩展表条目失败，orderExtraDTOList="+ JsonUtils.toJson(baseOrderExtraDTOList));
        return RemoteResponse.<Integer>custom().setSuccess().setData(successCount);
    }

    @Override
    public RemoteResponse<Boolean> saveList(List<BaseOrderExtraDTO> baseOrderExtraDTOList) {
        if(CollectionUtils.isEmpty(baseOrderExtraDTOList)){
            return RemoteResponse.success();
        }
        List<OrderExtraDO> orderExtraDOList = OrderExtraTranslator.dtoListToDoList(baseOrderExtraDTOList);
        Set<Integer> extraKeySet = orderExtraDOList.stream().map(OrderExtraDO::getExtraKey).filter(Objects::nonNull).collect(Collectors.toSet());
        for(Integer extraKey : extraKeySet){
            List<OrderExtraDO> extraDOList = orderExtraDOList.stream().filter(orderExtraDO -> extraKey.equals(orderExtraDO.getExtraKey()) && orderExtraDO.getOrderId() != null).collect(Collectors.toList());
            List<Integer> orderIdList = extraDOList.stream().map(OrderExtraDO::getOrderId).collect(Collectors.toList());
            List<OrderExtraDO> existExtraList = orderExtraMapper.selectByOrderIdInAndExtraKey(orderIdList, extraKey);

            List<Integer> existOrderIdList = existExtraList.stream().map(OrderExtraDO::getOrderId).collect(Collectors.toList());
            List<OrderExtraDO> toInsertExtraList = extraDOList.stream()
                    .filter(orderExtraDO -> !existOrderIdList.contains(orderExtraDO.getOrderId()))
                    .collect(Collectors.toList());
            List<OrderExtraDO> toUpdateExtraList = extraDOList.stream()
                    .filter(orderExtraDO -> existOrderIdList.contains(orderExtraDO.getOrderId()))
                    .collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(toInsertExtraList)){
                orderExtraMapper.insertList(toInsertExtraList);
            }
            if(CollectionUtils.isNotEmpty(toUpdateExtraList)){
                orderExtraMapper.batchUpdateExtraValue(toUpdateExtraList);
            }
        }
        return RemoteResponse.success();
    }

    /**
     * 通过订单id集合选择条目
     *
     * @param orderIdCollection
     * @return
     */
    @Override
    public RemoteResponse<List<BaseOrderExtraDTO>> selectByOrderIdList(Collection<Integer> orderIdCollection) {
        Preconditions.notEmpty(orderIdCollection, "通过订单id集合选择条目入参不可为空");
        List<OrderExtraDO> orderExtraDOList = orderExtraMapper.selectByOrderIdIn(orderIdCollection);
        List<BaseOrderExtraDTO> baseOrderExtraDTOList = OrderExtraTranslator.doListToDtoList(orderExtraDOList);
        return RemoteResponse.<List<BaseOrderExtraDTO>>custom().setData(baseOrderExtraDTOList).setSuccess();
    }

    /**
     * 通过订单号集合选择条目
     *
     * @param orderNoCollection
     * @return
     */
    @Override
    public RemoteResponse<List<BaseOrderExtraDTO>> selectByOrderNoList(Collection<String> orderNoCollection) {
        Preconditions.notEmpty(orderNoCollection, "通过订单号集合选择条目入参不可为空");
        List<OrderExtraDO> orderExtraDOList = orderExtraMapper.selectByOrderNoIn(orderNoCollection);
        List<BaseOrderExtraDTO> baseOrderExtraDTOList = OrderExtraTranslator.doListToDtoList(orderExtraDOList);
        return RemoteResponse.<List<BaseOrderExtraDTO>>custom().setData(baseOrderExtraDTOList).setSuccess();
    }

    /**
     * 通过订单id和操作类型id（extra value）查询条目
     *
     * @param query
     * @return
     */
    @Override
    public RemoteResponse<List<BaseOrderExtraDTO>> selectByOrderIdAndExtraKey(BaseOrderExtraDTO query) {
        Preconditions.isTrue(query != null && query.getOrderId() != null, "通过订单id和额外操作id选择条目的订单id入参不可为空");
        List<OrderExtraDO> orderExtraDOList = orderExtraMapper.selectByOrderIdAndExtraKey(query.getOrderId(), query.getExtraKey());
        List<BaseOrderExtraDTO> baseOrderExtraDTOList = OrderExtraTranslator.doListToDtoList(orderExtraDOList);
        return RemoteResponse.<List<BaseOrderExtraDTO>>custom().setData(baseOrderExtraDTOList).setSuccess();
    }

    /**
     * 通过订单id列表和操作类型id（extra value）查询条目
     * @param orderIdList
     * @param extraKey
     * @return
     */
    @Override
    public RemoteResponse<List<BaseOrderExtraDTO>> selectByOrderIdInAndExtraKey(Collection<Integer> orderIdList, Integer extraKey) {
        Preconditions.notEmpty(orderIdList, "通过订单id和额外操作id选择条目的订单id入参不可为空");
        List<OrderExtraDO> orderExtraDOList = orderExtraMapper.selectByOrderIdInAndExtraKey(orderIdList, extraKey);
        List<BaseOrderExtraDTO> baseOrderExtraDTOList = OrderExtraTranslator.doListToDtoList(orderExtraDOList);
        return RemoteResponse.<List<BaseOrderExtraDTO>>custom().setData(baseOrderExtraDTOList).setSuccess();
    }

    @Override
    public RemoteResponse<Boolean> deleteInOrderId(Collection<Integer> orderIdCol) {
        Preconditions.isTrue(orderIdCol.size() <= 200, "订单id上限为200个");
        orderExtraMapper.deleteInOrderId(orderIdCol);
        return RemoteResponse.success();
    }

    @Override
    public synchronized RemoteResponse<Boolean> appendToListExtraValue(AppendListExtraValueRequest request) {
        Preconditions.notNull(request, "请求参数不能为空");
        Preconditions.notNull(request.getOrderId(), "订单ID不能为空");
        Preconditions.notNull(request.getExtraKey(), "扩展键不能为空");
        Preconditions.notEmpty(request.getAppendValues(), "追加的值列表不能为空");

        Integer orderId = request.getOrderId();
        Integer extraKey = request.getExtraKey();
        List<Object> appendValues = request.getAppendValues();

        // 查询现有数据
        List<OrderExtraDO> existingList = orderExtraMapper.selectByOrderIdAndExtraKey(orderId, extraKey);

        List<Object> currentList = new ArrayList<>();
        OrderExtraDO existingRecord = null;

        if (CollectionUtils.isNotEmpty(existingList)) {
            existingRecord = existingList.get(0);
            String existingValue = existingRecord.getExtraValue();
            if (StringUtils.isNotBlank(existingValue)) {
                currentList = JsonUtils.parseArray(existingValue, Object.class);
                if (currentList == null) {
                    currentList = new ArrayList<>();
                }
            }
        }

        // 追加新值（去重）
        for (Object appendValue : appendValues) {
            if (!currentList.contains(appendValue)) {
                currentList.add(appendValue);
            }
        }

        String newJsonValue = JsonUtils.toJson(currentList);

        if (existingRecord != null) {
            existingRecord.setExtraValue(newJsonValue);
            orderExtraMapper.batchUpdateExtraValue(Collections.singletonList(existingRecord));
        } else {
            OrderExtraEnum orderExtraEnum = OrderExtraEnum.getByValue(extraKey);
            OrderExtraDO newRecord = new OrderExtraDO();
            newRecord.setOrderId(orderId);
            newRecord.setExtraKey(extraKey);
            newRecord.setExtraKeyDesc(orderExtraEnum != null ? orderExtraEnum.getDesc() : "");
            newRecord.setExtraValue(newJsonValue);
            orderExtraMapper.insertList(Collections.singletonList(newRecord));
        }

        return RemoteResponse.success();
    }

    @Override
    public RemoteResponse<ListExtraValueResponse> getListExtraValue(GetListExtraValueRequest request) {
        Preconditions.notNull(request, "请求参数不能为空");
        Preconditions.notNull(request.getOrderId(), "订单ID不能为空");
        Preconditions.notEmpty(request.getExtraKeys(), "扩展键列表不能为空");

        Integer orderId = request.getOrderId();
        List<Integer> extraKeys = request.getExtraKeys();

        Map<Integer, List<Object>> extraValueMap = new HashMap<>();

        for (Integer extraKey : extraKeys) {
            List<OrderExtraDO> existingList = orderExtraMapper.selectByOrderIdAndExtraKey(orderId, extraKey);

            List<Object> resultList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(existingList)) {
                OrderExtraDO record = existingList.get(0);
                String jsonValue = record.getExtraValue();
                if (StringUtils.isNotBlank(jsonValue)) {
                    List<Object> parsedList = JsonUtils.parseArray(jsonValue, Object.class);
                    if (parsedList != null) {
                        resultList = parsedList;
                    }
                }
            }
            extraValueMap.put(extraKey, resultList);
        }

        ListExtraValueResponse response = new ListExtraValueResponse();
        response.setOrderId(orderId);
        response.setExtraValueMap(extraValueMap);

        return RemoteResponse.<ListExtraValueResponse>custom().setSuccess().setData(response);
    }
}
