package com.reagent.order.rpc.orderextra;

import com.reagent.order.base.order.dto.BaseOrderExtraDTO;
import com.reagent.order.base.order.mapper.OrderExtraMapper;
import com.reagent.order.base.order.model.OrderExtraDO;
import com.reagent.order.base.order.service.OrderExtraRpcService;
import com.reagent.order.base.order.translator.OrderExtraTranslator;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.store.order.api.base.orderextra.enums.OrderExtraEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: Zeng <PERSON>
 * @Description:
 * @DateTime: 2021/6/28 16:00
 */
@MSharpService
public class OrderExtraRpcServiceImpl implements OrderExtraRpcService {

    private static final Logger logger = LoggerFactory.getLogger(OrderExtraRpcServiceImpl.class);

    @Resource
    private OrderExtraMapper orderExtraMapper;

    /**
     * 批量插入列表
     *
     * @param baseOrderExtraDTOList
     * @return
     */
    @Override
    public RemoteResponse<Integer> insertList(List<BaseOrderExtraDTO> baseOrderExtraDTOList) {
        Preconditions.notEmpty(baseOrderExtraDTOList, "批量插入订单扩展表入参不可为空");
        List<OrderExtraDO> orderExtraDOList = OrderExtraTranslator.dtoListToDoList(baseOrderExtraDTOList);
        int successCount = orderExtraMapper.insertList(orderExtraDOList);
        Preconditions.isTrue(successCount > 0, "插入订单扩展表条目失败，orderExtraDTOList="+ JsonUtils.toJson(baseOrderExtraDTOList));
        return RemoteResponse.<Integer>custom().setSuccess().setData(successCount);
    }

    @Override
    public RemoteResponse<Boolean> saveList(List<BaseOrderExtraDTO> baseOrderExtraDTOList) {
        if(CollectionUtils.isEmpty(baseOrderExtraDTOList)){
            return RemoteResponse.success();
        }
        List<OrderExtraDO> orderExtraDOList = OrderExtraTranslator.dtoListToDoList(baseOrderExtraDTOList);
        Set<Integer> extraKeySet = orderExtraDOList.stream().map(OrderExtraDO::getExtraKey).filter(Objects::nonNull).collect(Collectors.toSet());
        for(Integer extraKey : extraKeySet){
            List<OrderExtraDO> extraDOList = orderExtraDOList.stream().filter(orderExtraDO -> extraKey.equals(orderExtraDO.getExtraKey()) && orderExtraDO.getOrderId() != null).collect(Collectors.toList());
            List<Integer> orderIdList = extraDOList.stream().map(OrderExtraDO::getOrderId).collect(Collectors.toList());
            List<OrderExtraDO> existExtraList = orderExtraMapper.selectByOrderIdInAndExtraKey(orderIdList, extraKey);

            List<Integer> existOrderIdList = existExtraList.stream().map(OrderExtraDO::getOrderId).collect(Collectors.toList());
            List<OrderExtraDO> toInsertExtraList = extraDOList.stream()
                    .filter(orderExtraDO -> !existOrderIdList.contains(orderExtraDO.getOrderId()))
                    .collect(Collectors.toList());
            List<OrderExtraDO> toUpdateExtraList = extraDOList.stream()
                    .filter(orderExtraDO -> existOrderIdList.contains(orderExtraDO.getOrderId()))
                    .collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(toInsertExtraList)){
                orderExtraMapper.insertList(toInsertExtraList);
            }
            if(CollectionUtils.isNotEmpty(toUpdateExtraList)){
                orderExtraMapper.batchUpdateExtraValue(toUpdateExtraList);
            }
        }
        return RemoteResponse.success();
    }

    /**
     * 通过订单id集合选择条目
     *
     * @param orderIdCollection
     * @return
     */
    @Override
    public RemoteResponse<List<BaseOrderExtraDTO>> selectByOrderIdList(Collection<Integer> orderIdCollection) {
        Preconditions.notEmpty(orderIdCollection, "通过订单id集合选择条目入参不可为空");
        List<OrderExtraDO> orderExtraDOList = orderExtraMapper.selectByOrderIdIn(orderIdCollection);
        List<BaseOrderExtraDTO> baseOrderExtraDTOList = OrderExtraTranslator.doListToDtoList(orderExtraDOList);
        return RemoteResponse.<List<BaseOrderExtraDTO>>custom().setData(baseOrderExtraDTOList).setSuccess();
    }

    /**
     * 通过订单号集合选择条目
     *
     * @param orderNoCollection
     * @return
     */
    @Override
    public RemoteResponse<List<BaseOrderExtraDTO>> selectByOrderNoList(Collection<String> orderNoCollection) {
        Preconditions.notEmpty(orderNoCollection, "通过订单号集合选择条目入参不可为空");
        List<OrderExtraDO> orderExtraDOList = orderExtraMapper.selectByOrderNoIn(orderNoCollection);
        List<BaseOrderExtraDTO> baseOrderExtraDTOList = OrderExtraTranslator.doListToDtoList(orderExtraDOList);
        return RemoteResponse.<List<BaseOrderExtraDTO>>custom().setData(baseOrderExtraDTOList).setSuccess();
    }

    /**
     * 通过订单id和操作类型id（extra value）查询条目
     *
     * @param query
     * @return
     */
    @Override
    public RemoteResponse<List<BaseOrderExtraDTO>> selectByOrderIdAndExtraKey(BaseOrderExtraDTO query) {
        Preconditions.isTrue(query != null && query.getOrderId() != null, "通过订单id和额外操作id选择条目的订单id入参不可为空");
        List<OrderExtraDO> orderExtraDOList = orderExtraMapper.selectByOrderIdAndExtraKey(query.getOrderId(), query.getExtraKey());
        List<BaseOrderExtraDTO> baseOrderExtraDTOList = OrderExtraTranslator.doListToDtoList(orderExtraDOList);
        return RemoteResponse.<List<BaseOrderExtraDTO>>custom().setData(baseOrderExtraDTOList).setSuccess();
    }

    /**
     * 通过订单id列表和操作类型id（extra value）查询条目
     * @param orderIdList
     * @param extraKey
     * @return
     */
    @Override
    public RemoteResponse<List<BaseOrderExtraDTO>> selectByOrderIdInAndExtraKey(Collection<Integer> orderIdList, Integer extraKey) {
        Preconditions.notEmpty(orderIdList, "通过订单id和额外操作id选择条目的订单id入参不可为空");
        List<OrderExtraDO> orderExtraDOList = orderExtraMapper.selectByOrderIdInAndExtraKey(orderIdList, extraKey);
        List<BaseOrderExtraDTO> baseOrderExtraDTOList = OrderExtraTranslator.doListToDtoList(orderExtraDOList);
        return RemoteResponse.<List<BaseOrderExtraDTO>>custom().setData(baseOrderExtraDTOList).setSuccess();
    }

    @Override
    public RemoteResponse<Boolean> deleteInOrderId(Collection<Integer> orderIdCol) {
        Preconditions.isTrue(orderIdCol.size() <= 200, "订单id上限为200个");
        orderExtraMapper.deleteInOrderId(orderIdCol);
        return RemoteResponse.success();
    }

    /**
     * 追加元素到List类型的extraValue中
     * 使用synchronized关键字防止并发更新覆盖
     */
    @Override
    public synchronized RemoteResponse<Boolean> appendToListExtraValue(Integer orderId, Integer extraKey, List<Object> appendValues) {
        try {
            // 参数校验
            Preconditions.notNull(orderId, "订单ID不能为空");
            Preconditions.notNull(extraKey, "扩展键不能为空");
            Preconditions.notEmpty(appendValues, "追加的值列表不能为空");

            // 校验extraKey是否为List类型
            OrderExtraEnum orderExtraEnum = OrderExtraEnum.getByValue(extraKey);
            Preconditions.notNull(orderExtraEnum, "无效的扩展键: " + extraKey);
            Preconditions.isTrue(List.class.equals(orderExtraEnum.getDataTypeClass()),
                "扩展键 " + extraKey + " 不是List类型，当前类型: " + orderExtraEnum.getDataTypeClass().getSimpleName());

            logger.info("开始追加List类型数据，orderId: {}, extraKey: {}, appendValues: {}", orderId, extraKey, appendValues);

            // 查询现有数据
            List<OrderExtraDO> existingList = orderExtraMapper.selectByOrderIdAndExtraKey(orderId, extraKey);

            List<Object> currentList = new ArrayList<>();
            OrderExtraDO existingRecord = null;

            if (CollectionUtils.isNotEmpty(existingList)) {
                existingRecord = existingList.get(0);
                String existingValue = existingRecord.getExtraValue();

                if (StringUtils.isNotBlank(existingValue)) {
                    try {
                        // 解析现有的JSON数据
                        currentList = JsonUtils.parseArray(existingValue, Object.class);
                        if (currentList == null) {
                            currentList = new ArrayList<>();
                        }
                    } catch (Exception e) {
                        logger.warn("解析现有JSON数据失败，将创建新的List，orderId: {}, extraKey: {}, existingValue: {}",
                            orderId, extraKey, existingValue, e);
                        currentList = new ArrayList<>();
                    }
                }
            }

            // 追加新值（去重）
            for (Object appendValue : appendValues) {
                if (!currentList.contains(appendValue)) {
                    currentList.add(appendValue);
                }
            }

            // 序列化为JSON
            String newJsonValue = JsonUtils.toJson(currentList);

            if (existingRecord != null) {
                // 更新现有记录
                existingRecord.setExtraValue(newJsonValue);
                List<OrderExtraDO> updateList = Collections.singletonList(existingRecord);
                orderExtraMapper.batchUpdateExtraValue(updateList);
                logger.info("更新List类型数据成功，orderId: {}, extraKey: {}, newValue: {}", orderId, extraKey, newJsonValue);
            } else {
                // 创建新记录
                OrderExtraDO newRecord = new OrderExtraDO();
                newRecord.setOrderId(orderId);
                newRecord.setExtraKey(extraKey);
                newRecord.setExtraKeyDesc(orderExtraEnum.getDesc());
                newRecord.setExtraValue(newJsonValue);

                List<OrderExtraDO> insertList = Collections.singletonList(newRecord);
                orderExtraMapper.insertList(insertList);
                logger.info("创建List类型数据成功，orderId: {}, extraKey: {}, newValue: {}", orderId, extraKey, newJsonValue);
            }

            return RemoteResponse.success();

        } catch (Exception e) {
            logger.error("追加List类型数据失败，orderId: {}, extraKey: {}, appendValues: {}", orderId, extraKey, appendValues, e);
            return RemoteResponse.<Boolean>custom().setFail().setMessage("追加List类型数据失败: " + e.getMessage());
        }
    }

    /**
     * 查询List类型的extraValue并解析为List
     */
    @Override
    public RemoteResponse<List<Object>> getListExtraValue(Integer orderId, Integer extraKey) {
        try {
            // 参数校验
            Preconditions.notNull(orderId, "订单ID不能为空");
            Preconditions.notNull(extraKey, "扩展键不能为空");

            // 校验extraKey是否为List类型
            OrderExtraEnum orderExtraEnum = OrderExtraEnum.getByValue(extraKey);
            Preconditions.notNull(orderExtraEnum, "无效的扩展键: " + extraKey);
            Preconditions.isTrue(List.class.equals(orderExtraEnum.getDataTypeClass()),
                "扩展键 " + extraKey + " 不是List类型，当前类型: " + orderExtraEnum.getDataTypeClass().getSimpleName());

            logger.info("开始查询List类型数据，orderId: {}, extraKey: {}", orderId, extraKey);

            // 查询数据
            List<OrderExtraDO> existingList = orderExtraMapper.selectByOrderIdAndExtraKey(orderId, extraKey);

            if (CollectionUtils.isEmpty(existingList)) {
                logger.info("未找到List类型数据，返回空列表，orderId: {}, extraKey: {}", orderId, extraKey);
                return RemoteResponse.<List<Object>>custom().setSuccess().setData(new ArrayList<>());
            }

            OrderExtraDO record = existingList.get(0);
            String jsonValue = record.getExtraValue();

            if (StringUtils.isBlank(jsonValue)) {
                logger.info("List类型数据为空，返回空列表，orderId: {}, extraKey: {}", orderId, extraKey);
                return RemoteResponse.<List<Object>>custom().setSuccess().setData(new ArrayList<>());
            }

            try {
                // 解析JSON数据
                List<Object> resultList = JsonUtils.parseArray(jsonValue, Object.class);
                if (resultList == null) {
                    resultList = new ArrayList<>();
                }

                logger.info("查询List类型数据成功，orderId: {}, extraKey: {}, resultSize: {}", orderId, extraKey, resultList.size());
                return RemoteResponse.<List<Object>>custom().setSuccess().setData(resultList);

            } catch (Exception e) {
                logger.error("解析List类型JSON数据失败，orderId: {}, extraKey: {}, jsonValue: {}", orderId, extraKey, jsonValue, e);
                return RemoteResponse.<List<Object>>custom().setFail().setMessage("解析List类型数据失败: " + e.getMessage());
            }

        } catch (Exception e) {
            logger.error("查询List类型数据失败，orderId: {}, extraKey: {}", orderId, extraKey, e);
            return RemoteResponse.<List<Object>>custom().setFail().setMessage("查询List类型数据失败: " + e.getMessage());
        }
    }
}
