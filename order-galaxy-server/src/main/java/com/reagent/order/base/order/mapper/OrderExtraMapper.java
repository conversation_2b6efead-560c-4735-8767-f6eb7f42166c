package com.reagent.order.base.order.mapper;
import java.util.Collection;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

import com.reagent.order.base.order.model.OrderExtraDO;

@Mapper
public interface OrderExtraMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(OrderExtraDO record);

    int insertSelective(OrderExtraDO record);

    OrderExtraDO selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(OrderExtraDO record);

    int updateByPrimaryKey(OrderExtraDO record);

    /**
     * 插入数据，不传入id，create time，update time
     * @param list
     * @return
     */
    int insertList(@Param("list")List<OrderExtraDO> list);

    /**
     * 批量update extraValue
     * @param list 列表
     * @return 更新行数
     */
    int batchUpdateExtraValue(@Param("list")List<OrderExtraDO> list);

    /**
     * 通过orderid集合选择条目
     * @param orderIdCollection
     * @return
     */
    List<OrderExtraDO> selectByOrderIdIn(@Param("orderIdCollection")Collection<Integer> orderIdCollection);

    /**
     * 通过orderno集合选择条目
     * @param orderNoCollection
     * @return
     */
	List<OrderExtraDO> selectByOrderNoIn(@Param("orderNoCollection")Collection<String> orderNoCollection);

    /**
     * 通过orderno集合选择条目
     * @param orderNoCollection
     * @return
     */
    List<OrderExtraDO> selectByOrderNoListAndExtraKey(@Param("orderNoCollection")Collection<String> orderNoCollection, @Param("extraKey")Integer extraKey);

    /**
     * 通过orderId（不可为空）和额外key（可以为空）查询条目
     * @param orderId
     * @param extraKey
     * @return
     */
	List<OrderExtraDO> selectByOrderIdAndExtraKey(@Param("orderId")Integer orderId,@Param("extraKey")Integer extraKey);

    /**
     * 通过orderId列表（不可为空）和额外key（可以为空）查询条目
     * @param orderIdCollection
     * @param extraKey
     * @return
     */
	List<OrderExtraDO> selectByOrderIdInAndExtraKey(@Param("orderIdCollection")Collection<Integer> orderIdCollection,@Param("extraKey")Integer extraKey);

    void deleteInOrderId(@Param("orderIdCol") Collection<Integer> orderIdCol);
}